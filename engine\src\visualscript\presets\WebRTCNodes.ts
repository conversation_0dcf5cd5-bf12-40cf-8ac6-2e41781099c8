/**
 * 视觉脚本WebRTC节点
 * 提供WebRTC相关功能，如媒体流控制、数据通道管理等
 */
import { AsyncNode } from '../nodes/AsyncNode';
import { EventNode } from '../nodes/EventNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NetworkSystem } from '../../network/NetworkSystem';
import { WebRTCConnection } from '../../network/WebRTCConnection';
import { WebRTCDataChannel } from '../../network/WebRTCDataChannel';

/**
 * 创建WebRTC连接节点
 * 创建与远程对等方的WebRTC连接
 */
export class CreateWebRTCConnectionNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'peerId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '对等方ID'
    });

    this.addInput({
      name: 'config',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebRTC配置',
      defaultValue: {},
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '创建失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'WebRTC连接'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const peerId = this.getInputValue('peerId') as string;

    // 检查输入值是否有效
    if (!peerId) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取网络系统
    const networkSystem = this.context.world.getSystem(NetworkSystem);
    if (!networkSystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 创建WebRTC连接
      const connection = networkSystem.createWebRTCConnection(peerId);
      
      if (connection) {
        // 设置输出值
        this.setOutputValue('connection', connection);
        
        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('创建WebRTC连接失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 发送数据通道消息节点
 * 通过WebRTC连接发送数据
 */
export class SendDataChannelMessageNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'WebRTC连接'
    });

    this.addInput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '要发送的消息'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '发送成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '发送失败'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const connection = this.getInputValue('connection') as WebRTCConnection;
    const message = this.getInputValue('message');

    // 检查输入值是否有效
    if (!connection || message === undefined) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 发送消息
      await connection.send(message);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('发送数据通道消息失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 数据通道消息事件节点
 * 监听数据通道消息事件
 */
export class DataChannelMessageEventNode extends EventNode {
  /** 数据通道 */
  private dataChannel: WebRTCDataChannel | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入数据插槽
    this.addInput({
      name: 'dataChannel',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '数据通道'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '收到消息'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '消息数据'
    });
  }

  /**
   * 初始化事件
   */
  public initialize(): void {
    // 获取数据通道
    this.dataChannel = this.getInputValue('dataChannel') as WebRTCDataChannel;
    
    if (this.dataChannel) {
      // 监听消息事件
      this.dataChannel.on('message', this.onMessage.bind(this));
    }
  }

  /**
   * 消息事件处理
   * @param message 消息数据
   */
  private onMessage(message: any): void {
    // 设置输出值
    this.setOutputValue('message', message);
    
    // 触发输出流程
    this.triggerFlow('flow');
  }

  /**
   * 清理事件
   */
  public cleanup(): void {
    if (this.dataChannel) {
      // 移除事件监听
      this.dataChannel.off('message', this.onMessage.bind(this));
      this.dataChannel = null;
    }
  }
}

/**
 * 获取媒体流节点
 * 获取用户媒体流（摄像头、麦克风）
 */
export class GetUserMediaNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'enableVideo',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '启用视频',
      defaultValue: true
    });

    this.addInput({
      name: 'enableAudio',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '启用音频',
      defaultValue: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '获取成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '获取失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'mediaStream',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '媒体流'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const enableVideo = this.getInputValue('enableVideo') as boolean;
    const enableAudio = this.getInputValue('enableAudio') as boolean;

    try {
      // 获取用户媒体流
      const stream = await navigator.mediaDevices.getUserMedia({
        video: enableVideo,
        audio: enableAudio
      });

      // 设置输出值
      this.setOutputValue('mediaStream', stream);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('获取媒体流失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 屏幕共享节点
 * 获取屏幕共享流
 */
export class GetDisplayMediaNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'enableAudio',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '启用音频',
      defaultValue: false
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '获取成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '获取失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'displayStream',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '屏幕共享流'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const enableAudio = this.getInputValue('enableAudio') as boolean;

    try {
      // 获取屏幕共享流
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: enableAudio
      });

      // 设置输出值
      this.setOutputValue('displayStream', stream);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('获取屏幕共享流失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 注册WebRTC节点
 * @param registry 节点注册表
 */
export function registerWebRTCNodes(registry: NodeRegistry): void {
  // 注册创建WebRTC连接节点
  registry.registerNodeType({
    type: 'network/webrtc/createConnection',
    category: NodeCategory.NETWORK,
    constructor: CreateWebRTCConnectionNode,
    label: '创建WebRTC连接',
    description: '创建与远程对等方的WebRTC连接',
    icon: 'webrtc',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'connection']
  });

  // 注册发送数据通道消息节点
  registry.registerNodeType({
    type: 'network/webrtc/sendDataChannelMessage',
    category: NodeCategory.NETWORK,
    constructor: SendDataChannelMessageNode,
    label: '发送数据通道消息',
    description: '通过WebRTC连接发送数据',
    icon: 'send',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'datachannel', 'send']
  });

  // 注册数据通道消息事件节点
  registry.registerNodeType({
    type: 'network/webrtc/onDataChannelMessage',
    category: NodeCategory.NETWORK,
    constructor: DataChannelMessageEventNode,
    label: '数据通道消息事件',
    description: '监听数据通道消息事件',
    icon: 'message',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'datachannel', 'message', 'event']
  });

  // 注册获取用户媒体节点
  registry.registerNodeType({
    type: 'network/webrtc/getUserMedia',
    category: NodeCategory.NETWORK,
    constructor: GetUserMediaNode,
    label: '获取用户媒体',
    description: '获取用户媒体流（摄像头、麦克风）',
    icon: 'camera',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'media', 'camera', 'microphone']
  });

  // 注册获取屏幕共享节点
  registry.registerNodeType({
    type: 'network/webrtc/getDisplayMedia',
    category: NodeCategory.NETWORK,
    constructor: GetDisplayMediaNode,
    label: '获取屏幕共享',
    description: '获取屏幕共享流',
    icon: 'screen',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'screen', 'share']
  });
}
